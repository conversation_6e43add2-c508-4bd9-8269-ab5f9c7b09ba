function addNumbers()
{
    var firstNum = parseFloat(document.getElementById("firstNumber").value);
    var secondNum = parseFloat(document.getElementById("secondNumber").value);
    var result = firstNum + secondNum;
    document.getElementById("result").innerHTML = result;
}

function subNumbers()
{
    var firstNum = parseFloat(document.getElementById("firstNumber").value);
    var secondNum = parseFloat(document.getElementById("secondNumber").value);
    var result = firstNum - secondNum;
    document.getElementById("result").innerHTML = result;
}

function multiplyBy()
{
    var firstNum = parseFloat(document.getElementById("firstNumber").value);
    var secondNum = parseFloat(document.getElementById("secondNumber").value);
    var result = firstNum * secondNum;
    document.getElementById("result").innerHTML = result;
}

function divideBy()
{
    var firstNum = parseFloat(document.getElementById("firstNumber").value);
    var secondNum = parseFloat(document.getElementById("secondNumber").value);
    var result = firstNum / secondNum;
    document.getElementById("result").innerHTML = result;
}

function modulusOf()
{
    var firstNum = parseFloat(document.getElementById("firstNumber").value);
    var secondNum = parseFloat(document.getElementById("secondNumber").value);
    var result = firstNum % secondNum;
    document.getElementById("result").innerHTML = result;
}